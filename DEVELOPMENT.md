# 🚀 Development Guide - PSG-BMI Portal

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 16 or higher)
- **npm** or **yarn** package manager
- **Expo CLI** (`npm install -g expo-cli`)
- **Git** for version control

### Platform-specific Requirements

#### For iOS Development
- **Xcode** (latest version)
- **iOS Simulator** or physical iOS device
- **Apple Developer Account** (for device testing)

#### For Android Development
- **Android Studio**
- **Android SDK** (API level 21 or higher)
- **Android Emulator** or physical Android device

## 🛠 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone [repository-url]
   cd psg-bmi
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start the development server**
   ```bash
   npm start
   # or
   expo start
   ```

4. **Run on specific platforms**
   ```bash
   # iOS Simulator
   npm run ios
   
   # Android Emulator
   npm run android
   
   # Web Browser
   npm run web
   ```

## 📁 Project Structure

```
psg-bmi/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── Button.js       # Custom button component
│   │   ├── Card.js         # Card container component
│   │   ├── MenuCard.js     # Menu grid item component
│   │   ├── StatCard.js     # Statistics display component
│   │   ├── Header.js       # App header with profile
│   │   ├── MenuGrid.js     # Main menu grid
│   │   ├── Carousel.js     # Auto-scroll carousel
│   │   ├── DashboardOverview.js  # Dashboard stats
│   │   ├── VideoGallery.js # Video content gallery
│   │   ├── RecentActivities.js  # Activity timeline
│   │   ├── BottomNavigation.js  # Bottom nav with FAB
│   │   ├── ErrorBoundary.js     # Error handling
│   │   └── index.js        # Component exports
│   ├── screens/            # Screen components
│   │   └── HomeScreen.js   # Main home screen
│   ├── constants/          # App constants
│   │   ├── Colors.js       # Color palette
│   │   ├── Typography.js   # Text styles
│   │   ├── Spacing.js      # Layout spacing
│   │   ├── Animations.js   # Animation configs
│   │   ├── MenuData.js     # Menu and data constants
│   │   └── index.js        # Constants exports
│   ├── utils/              # Utility functions
│   │   ├── accessibility.js # A11y helpers
│   │   ├── performance.js  # Performance utilities
│   │   └── index.js        # Utils exports
│   └── styles/             # Global styles (future use)
├── assets/                 # Static assets
├── App.js                  # Main app component
├── app.json               # Expo configuration
├── package.json           # Dependencies
└── babel.config.js        # Babel configuration
```

## 🎨 Design System

### Color Palette
- **Primary**: #2196F3 (Material Blue)
- **Background**: #F8F9FA (Light Gray)
- **Surface**: #FFFFFF (Pure White)
- **Success**: #4CAF50
- **Warning**: #FF9800
- **Error**: #FF5252

### Typography Scale
- **H1**: 24px, weight 700
- **H2**: 20px, weight 600
- **H3**: 18px, weight 600
- **Body1**: 16px, weight 400
- **Body2**: 14px, weight 400
- **Caption**: 12px, weight 400

### Spacing System (8px Grid)
- **xs**: 4px
- **sm**: 8px
- **md**: 16px
- **lg**: 24px
- **xl**: 32px

## 🔧 Development Guidelines

### Component Development
1. Use functional components with hooks
2. Implement proper TypeScript types (future enhancement)
3. Add accessibility props for screen readers
4. Use native driver for animations
5. Implement proper error boundaries

### Performance Best Practices
1. Use `React.memo` for expensive components
2. Implement `useCallback` and `useMemo` appropriately
3. Use `FlatList` optimizations for large lists
4. Enable `removeClippedSubviews` for scrollable content
5. Use native driver for all animations

### Accessibility Guidelines
1. Add `accessibilityLabel` to all interactive elements
2. Use `accessibilityRole` appropriately
3. Implement `accessibilityState` for dynamic content
4. Test with screen readers enabled
5. Ensure minimum touch target size (44px)

## 🧪 Testing

### Manual Testing Checklist
- [ ] App launches without errors
- [ ] All menu items are clickable
- [ ] Carousel auto-scrolls and pauses on interaction
- [ ] Dashboard cards display correct data
- [ ] Video gallery loads thumbnails
- [ ] Activities timeline shows recent items
- [ ] Bottom navigation works correctly
- [ ] FAB shows quick actions menu
- [ ] Header profile and notifications work
- [ ] Pull-to-refresh functionality works
- [ ] App works on different screen sizes
- [ ] Accessibility features work with screen reader

### Performance Testing
- [ ] App starts in under 2 seconds
- [ ] Animations run at 60fps
- [ ] Memory usage stays reasonable
- [ ] No memory leaks during navigation
- [ ] Smooth scrolling in all lists

## 🚀 Deployment

### Development Build
```bash
expo build:android
expo build:ios
```

### Production Build
```bash
expo build:android --type app-bundle
expo build:ios --type archive
```

## 🐛 Troubleshooting

### Common Issues

1. **Metro bundler cache issues**
   ```bash
   expo start -c
   ```

2. **Node modules issues**
   ```bash
   rm -rf node_modules
   npm install
   ```

3. **iOS simulator not working**
   ```bash
   xcrun simctl erase all
   ```

4. **Android emulator issues**
   - Ensure Android SDK is properly configured
   - Check that emulator has enough RAM allocated

### Debug Tools
- **React Native Debugger**
- **Flipper** for advanced debugging
- **Expo DevTools** in browser
- **Chrome DevTools** for web version

## 📞 Support

For technical issues or questions:
1. Check existing GitHub issues
2. Create new issue with detailed description
3. Include device/platform information
4. Provide steps to reproduce

---

**Happy Coding! 🎉**
