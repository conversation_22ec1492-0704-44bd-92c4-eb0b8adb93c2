import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { Colors, Spacing, Animations } from '../constants';

const Card = ({
  children,
  style,
  onPress,
  variant = 'elevated',
  padding = 'md',
  margin = 'sm',
  borderRadius = 'lg',
  disabled = false,
  ...props
}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (onPress && !disabled) {
      Animated.spring(scaleValue, {
        toValue: Animations.scale.pressed,
        ...Animations.spring.default,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (onPress && !disabled) {
      Animated.spring(scaleValue, {
        toValue: Animations.scale.normal,
        ...Animations.spring.default,
        useNativeDriver: true,
      }).start();
    }
  };

  const getCardStyle = () => {
    const baseStyle = [
      styles.card,
      styles[`padding_${padding}`],
      styles[`margin_${margin}`],
      styles[`borderRadius_${borderRadius}`],
    ];

    switch (variant) {
      case 'elevated':
        baseStyle.push(styles.elevatedCard);
        break;
      case 'outlined':
        baseStyle.push(styles.outlinedCard);
        break;
      case 'filled':
        baseStyle.push(styles.filledCard);
        break;
      default:
        baseStyle.push(styles.elevatedCard);
    }

    if (disabled) {
      baseStyle.push(styles.disabledCard);
    }

    return baseStyle;
  };

  const CardContent = () => (
    <View style={[getCardStyle(), style]} {...props}>
      {children}
    </View>
  );

  if (onPress && !disabled) {
    return (
      <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
          disabled={disabled}
        >
          <CardContent />
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return <CardContent />;
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.cardBackground,
  },
  
  // Padding variants
  padding_xs: {
    padding: Spacing.padding.xs,
  },
  padding_sm: {
    padding: Spacing.padding.sm,
  },
  padding_md: {
    padding: Spacing.padding.md,
  },
  padding_lg: {
    padding: Spacing.padding.lg,
  },
  padding_xl: {
    padding: Spacing.padding.xl,
  },
  
  // Margin variants
  margin_xs: {
    margin: Spacing.margin.xs,
  },
  margin_sm: {
    margin: Spacing.margin.sm,
  },
  margin_md: {
    margin: Spacing.margin.md,
  },
  margin_lg: {
    margin: Spacing.margin.lg,
  },
  margin_xl: {
    margin: Spacing.margin.xl,
  },
  
  // Border radius variants
  borderRadius_sm: {
    borderRadius: Spacing.borderRadius.sm,
  },
  borderRadius_md: {
    borderRadius: Spacing.borderRadius.md,
  },
  borderRadius_lg: {
    borderRadius: Spacing.borderRadius.lg,
  },
  borderRadius_xl: {
    borderRadius: Spacing.borderRadius.xl,
  },
  
  // Card variants
  elevatedCard: {
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  outlinedCard: {
    borderWidth: 1,
    borderColor: Colors.outline,
    shadowOpacity: 0,
    elevation: 0,
  },
  filledCard: {
    backgroundColor: Colors.surfaceVariant,
    shadowOpacity: 0,
    elevation: 0,
  },
  disabledCard: {
    opacity: Animations.opacity.disabled,
  },
});

export default Card;
