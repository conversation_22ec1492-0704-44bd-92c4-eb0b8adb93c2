import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import * as SplashScreen from 'expo-splash-screen';

// Import components
import ErrorBoundary from './src/components/ErrorBoundary';
import HomeScreen from './src/screens/HomeScreen';
import BottomNavigation from './src/components/BottomNavigation';
import ProtectedRoute from './src/components/ProtectedRoute';
import { AuthProvider } from './src/contexts/AuthContext';
import { Colors } from './src/constants';
import { initializeServices } from './src/services';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function App() {
  const [appIsReady, setAppIsReady] = React.useState(false);

  React.useEffect(() => {
    async function prepare() {
      try {
        console.log('Initializing PSG-BMI Portal...');

        // Initialize services
        const serviceResult = await initializeServices();

        if (serviceResult.success) {
          console.log('Services initialized successfully');
        } else {
          console.warn('Service initialization failed:', serviceResult.error);
        }

        // Simulate minimum loading time for better UX
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (e) {
        console.error('App initialization error:', e);
      } finally {
        // Tell the application to render
        setAppIsReady(true);
      }
    }

    prepare();
  }, []);

  const onLayoutRootView = React.useCallback(async () => {
    if (appIsReady) {
      // This tells the splash screen to hide immediately
      await SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <AuthProvider>
          <ProtectedRoute>
            <View style={styles.container} onLayout={onLayoutRootView}>
              <StatusBar style="light" backgroundColor={Colors.primary} />

              {/* Main Content - Now takes full height */}
              <HomeScreen />

              {/* Fixed Bottom Navigation */}
              <BottomNavigation />
            </View>
          </ProtectedRoute>
        </AuthProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
});
