/**
 * Services Index
 * Central export point for all services
 */

// API Client
export { apiClient, ApiError, API_CONFIG } from './api/apiClient';

// Authentication Service
export { authService, STORAGE_KEYS } from './auth/authService';

// Video Service
export { videoService } from './video/videoService';

// User Service
export { userService } from './user/userService';

// Service initialization and configuration
export const initializeServices = async () => {
  try {
    console.log('Initializing services...');
    
    // Restore authentication state
    const isAuthenticated = await authService.restoreAuthState();
    
    if (isAuthenticated) {
      console.log('Authentication state restored successfully');
    } else {
      console.log('No valid authentication state found');
    }
    
    // Clear expired caches
    videoService.clearCache();
    userService.clearCache();
    
    console.log('Services initialized successfully');
    
    return {
      success: true,
      isAuthenticated,
    };
    
  } catch (error) {
    console.error('Error initializing services:', error);
    return {
      success: false,
      error: error.message,
    };
  }
};

// Service health check
export const checkServiceHealth = async () => {
  const health = {
    api: false,
    auth: false,
    video: false,
    user: false,
  };
  
  try {
    // Check API client
    health.api = true;
    
    // Check auth service
    health.auth = authService.getIsAuthenticated();
    
    // Check video service (try to get categories)
    try {
      await videoService.getVideoCategories();
      health.video = true;
    } catch (error) {
      console.warn('Video service health check failed:', error);
    }
    
    // Check user service (if authenticated)
    if (health.auth) {
      try {
        await userService.getUserPreferences();
        health.user = true;
      } catch (error) {
        console.warn('User service health check failed:', error);
      }
    }
    
  } catch (error) {
    console.error('Service health check error:', error);
  }
  
  return health;
};

// Service configuration
export const configureServices = (config = {}) => {
  const {
    apiBaseUrl,
    apiTimeout,
    cacheExpiry,
  } = config;
  
  // Configure API client if needed
  if (apiBaseUrl) {
    apiClient.baseURL = apiBaseUrl;
  }
  
  if (apiTimeout) {
    apiClient.timeout = apiTimeout;
  }
  
  // Configure cache expiry if needed
  if (cacheExpiry) {
    videoService.cacheExpiry = cacheExpiry;
    userService.cacheExpiry = cacheExpiry;
  }
  
  console.log('Services configured with:', config);
};

// Export default services object
export default {
  api: apiClient,
  auth: authService,
  video: videoService,
  user: userService,
  initialize: initializeServices,
  checkHealth: checkServiceHealth,
  configure: configureServices,
};
