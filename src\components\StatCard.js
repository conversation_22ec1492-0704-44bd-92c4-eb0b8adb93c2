import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors, Typography, Spacing, Animations } from '../constants';

const StatCard = ({
  title,
  value,
  subtitle,
  icon,
  color = Colors.primary,
  trend = 'neutral',
  onPress,
  style,
}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    if (onPress) {
      Animated.spring(scaleValue, {
        toValue: Animations.scale.pressed,
        ...Animations.spring.default,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (onPress) {
      Animated.spring(scaleValue, {
        toValue: Animations.scale.normal,
        ...Animations.spring.default,
        useNativeDriver: true,
      }).start();
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'positive':
        return 'trending-up';
      case 'negative':
        return 'trending-down';
      default:
        return 'trending-flat';
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case 'positive':
        return Colors.success;
      case 'negative':
        return Colors.error;
      default:
        return Colors.onSurfaceVariant;
    }
  };

  const CardContent = () => (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
          <MaterialIcons
            name={icon}
            size={Spacing.iconSize.md}
            color={color}
          />
        </View>
        <MaterialIcons
          name={getTrendIcon()}
          size={Spacing.iconSize.sm}
          color={getTrendColor()}
        />
      </View>
      
      <View style={styles.content}>
        <Text style={styles.value}>{value}</Text>
        <Text style={styles.title} numberOfLines={1}>
          {title}
        </Text>
        <Text style={styles.subtitle} numberOfLines={1}>
          {subtitle}
        </Text>
      </View>
    </View>
  );

  if (onPress) {
    return (
      <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
        >
          <CardContent />
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return <CardContent />;
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.surface,
    borderRadius: Spacing.borderRadius.lg,
    padding: Spacing.padding.md,
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    minHeight: 120,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: Spacing.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  value: {
    ...Typography.h2,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  title: {
    ...Typography.body2,
    color: Colors.onSurface,
    marginBottom: Spacing.xs / 2,
  },
  subtitle: {
    ...Typography.caption,
    color: Colors.onSurfaceVariant,
  },
});

export default StatCard;
