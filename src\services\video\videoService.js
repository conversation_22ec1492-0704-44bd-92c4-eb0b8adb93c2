/**
 * Video Service
 * Handles video data fetching, caching, and management
 */

import { apiClient, ApiError } from '../api/apiClient';
import { PlaceholderImages } from '../../utils/placeholderImages';

class VideoService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
  }

  // Get featured videos
  async getFeaturedVideos(options = {}) {
    try {
      const { 
        limit = 10, 
        offset = 0, 
        category = null,
        forceRefresh = false 
      } = options;

      const cacheKey = `featured_videos_${limit}_${offset}_${category}`;
      
      // Check cache first
      if (!forceRefresh && this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockData = await this.getMockFeaturedVideos(options);
        this.setCache(cacheKey, mockData);
        return mockData;
      }

      // Real API call
      const params = { limit, offset };
      if (category) params.category = category;

      const response = await apiClient.get('/videos/featured', params);
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error fetching featured videos:', error);
      
      // Return cached data if available, otherwise throw
      const cacheKey = `featured_videos_${options.limit || 10}_${options.offset || 0}_${options.category}`;
      if (this.cache.has(cacheKey)) {
        console.warn('Returning cached data due to API error');
        return this.cache.get(cacheKey).data;
      }
      
      throw new ApiError(
        error.message || 'Failed to fetch featured videos',
        error.status || 500
      );
    }
  }

  // Get mock featured videos for development
  async getMockFeaturedVideos(options = {}) {
    const { limit = 10, offset = 0 } = options;
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const mockVideos = [
      {
        id: 1,
        title: 'Safety Training - Keselamatan Kerja',
        description: 'Pelatihan keselamatan kerja untuk semua karyawan',
        thumbnail: PlaceholderImages.video1,
        duration: '15:30',
        views: 1300,
        category: 'Training',
        tags: ['safety', 'training', 'mandatory'],
        uploadDate: '2024-01-15T10:00:00Z',
        instructor: 'Safety Team',
        featured: true,
        url: 'https://example.com/videos/safety-training',
      },
      {
        id: 2,
        title: 'Company Update - Q1 2024',
        description: 'Update perusahaan untuk kuartal pertama 2024',
        thumbnail: PlaceholderImages.video2,
        duration: '8:45',
        views: 890,
        category: 'Update',
        tags: ['company', 'quarterly', 'update'],
        uploadDate: '2024-01-10T14:30:00Z',
        instructor: 'Management Team',
        featured: true,
        url: 'https://example.com/videos/company-update-q1',
      },
      {
        id: 3,
        title: 'New Employee Orientation',
        description: 'Orientasi untuk karyawan baru PSG-BMI',
        thumbnail: PlaceholderImages.video3,
        duration: '22:15',
        views: 567,
        category: 'Orientation',
        tags: ['orientation', 'new-employee', 'onboarding'],
        uploadDate: '2024-01-08T09:00:00Z',
        instructor: 'HR Department',
        featured: true,
        url: 'https://example.com/videos/new-employee-orientation',
      },
      {
        id: 4,
        title: 'Production Excellence Workshop',
        description: 'Workshop peningkatan kualitas produksi',
        thumbnail: PlaceholderImages.video4,
        duration: '18:20',
        views: 432,
        category: 'Workshop',
        tags: ['production', 'excellence', 'quality'],
        uploadDate: '2024-01-05T11:15:00Z',
        instructor: 'Production Team',
        featured: true,
        url: 'https://example.com/videos/production-excellence',
      },
      {
        id: 5,
        title: 'Digital Transformation Initiative',
        description: 'Inisiatif transformasi digital perusahaan',
        thumbnail: PlaceholderImages.video1,
        duration: '12:45',
        views: 678,
        category: 'Technology',
        tags: ['digital', 'transformation', 'technology'],
        uploadDate: '2024-01-03T16:00:00Z',
        instructor: 'IT Department',
        featured: true,
        url: 'https://example.com/videos/digital-transformation',
      },
      {
        id: 6,
        title: 'Environmental Sustainability Program',
        description: 'Program keberlanjutan lingkungan PSG-BMI',
        thumbnail: PlaceholderImages.video2,
        duration: '14:30',
        views: 523,
        category: 'Environment',
        tags: ['environment', 'sustainability', 'green'],
        uploadDate: '2024-01-01T08:30:00Z',
        instructor: 'Environmental Team',
        featured: true,
        url: 'https://example.com/videos/environmental-sustainability',
      },
    ];

    // Apply pagination
    const startIndex = offset;
    const endIndex = startIndex + limit;
    const paginatedVideos = mockVideos.slice(startIndex, endIndex);

    return {
      videos: paginatedVideos,
      total: mockVideos.length,
      limit,
      offset,
      hasMore: endIndex < mockVideos.length,
    };
  }

  // Get video by ID
  async getVideoById(videoId) {
    try {
      const cacheKey = `video_${videoId}`;
      
      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockData = await this.getMockVideoById(videoId);
        this.setCache(cacheKey, mockData);
        return mockData;
      }

      // Real API call
      const response = await apiClient.get(`/videos/${videoId}`);
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error fetching video:', error);
      throw new ApiError(
        error.message || 'Failed to fetch video',
        error.status || 500
      );
    }
  }

  // Get mock video by ID for development
  async getMockVideoById(videoId) {
    await new Promise(resolve => setTimeout(resolve, 300));

    const featuredVideos = await this.getMockFeaturedVideos({ limit: 20 });
    const video = featuredVideos.videos.find(v => v.id === parseInt(videoId));

    if (!video) {
      throw new ApiError('Video not found', 404);
    }

    return video;
  }

  // Get video categories
  async getVideoCategories() {
    try {
      const cacheKey = 'video_categories';
      
      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockData = await this.getMockVideoCategories();
        this.setCache(cacheKey, mockData);
        return mockData;
      }

      // Real API call
      const response = await apiClient.get('/videos/categories');
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error fetching video categories:', error);
      throw new ApiError(
        error.message || 'Failed to fetch video categories',
        error.status || 500
      );
    }
  }

  // Get mock video categories for development
  async getMockVideoCategories() {
    await new Promise(resolve => setTimeout(resolve, 200));

    return [
      { id: 'training', name: 'Training', count: 15 },
      { id: 'update', name: 'Company Updates', count: 8 },
      { id: 'orientation', name: 'Orientation', count: 5 },
      { id: 'workshop', name: 'Workshops', count: 12 },
      { id: 'technology', name: 'Technology', count: 7 },
      { id: 'environment', name: 'Environment', count: 4 },
    ];
  }

  // Record video view
  async recordVideoView(videoId) {
    try {
      // For development - just log
      if (__DEV__) {
        console.log(`Recording view for video ${videoId}`);
        return { success: true };
      }

      // Real API call
      const response = await apiClient.post(`/videos/${videoId}/view`);
      return response.data;

    } catch (error) {
      console.error('Error recording video view:', error);
      // Don't throw error for view tracking failures
      return { success: false };
    }
  }

  // Search videos
  async searchVideos(query, options = {}) {
    try {
      const { limit = 10, offset = 0, category = null } = options;
      const cacheKey = `search_${query}_${limit}_${offset}_${category}`;
      
      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock search results
      if (__DEV__) {
        const mockData = await this.getMockSearchResults(query, options);
        this.setCache(cacheKey, mockData);
        return mockData;
      }

      // Real API call
      const params = { q: query, limit, offset };
      if (category) params.category = category;

      const response = await apiClient.get('/videos/search', params);
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error searching videos:', error);
      throw new ApiError(
        error.message || 'Failed to search videos',
        error.status || 500
      );
    }
  }

  // Get mock search results for development
  async getMockSearchResults(query, options = {}) {
    await new Promise(resolve => setTimeout(resolve, 400));

    const allVideos = await this.getMockFeaturedVideos({ limit: 20 });
    
    // Simple search implementation
    const filteredVideos = allVideos.videos.filter(video => 
      video.title.toLowerCase().includes(query.toLowerCase()) ||
      video.description.toLowerCase().includes(query.toLowerCase()) ||
      video.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    const { limit = 10, offset = 0 } = options;
    const startIndex = offset;
    const endIndex = startIndex + limit;
    const paginatedResults = filteredVideos.slice(startIndex, endIndex);

    return {
      videos: paginatedResults,
      total: filteredVideos.length,
      query,
      limit,
      offset,
      hasMore: endIndex < filteredVideos.length,
    };
  }

  // Cache management
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  isCacheValid(key) {
    if (!this.cache.has(key)) {
      return false;
    }
    
    const cached = this.cache.get(key);
    return (Date.now() - cached.timestamp) < this.cacheExpiry;
  }

  clearCache() {
    this.cache.clear();
  }

  // Get cache stats for debugging
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// Create singleton instance
const videoService = new VideoService();

export { videoService };
export default videoService;
