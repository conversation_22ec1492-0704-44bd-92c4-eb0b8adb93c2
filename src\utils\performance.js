// Performance monitoring and optimization utilities
import { InteractionManager } from 'react-native';

export const PerformanceMonitor = {
  // Track component render times
  trackRenderTime: (componentName, startTime) => {
    const endTime = Date.now();
    const renderTime = endTime - startTime;
    
    if (__DEV__) {
      console.log(`[Performance] ${componentName} rendered in ${renderTime}ms`);
      
      // Warn if render time is too long
      if (renderTime > 16) { // 60fps = 16.67ms per frame
        console.warn(`[Performance Warning] ${componentName} took ${renderTime}ms to render (>16ms)`);
      }
    }
    
    return renderTime;
  },

  // Defer heavy operations until after interactions
  runAfterInteractions: (callback) => {
    return InteractionManager.runAfterInteractions(callback);
  },

  // Create a performance-optimized timeout
  createOptimizedTimeout: (callback, delay) => {
    return InteractionManager.runAfterInteractions(() => {
      setTimeout(callback, delay);
    });
  },

  // Memory usage tracking (development only)
  trackMemoryUsage: (label) => {
    if (__DEV__ && global.performance && global.performance.memory) {
      const memory = global.performance.memory;
      console.log(`[Memory] ${label}:`, {
        used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
        total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`,
        limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`,
      });
    }
  },
};

// Animation performance utilities
export const AnimationUtils = {
  // Standard spring configuration for 60fps
  springConfig: {
    damping: 15,
    stiffness: 150,
    mass: 1,
    useNativeDriver: true,
  },

  // Fast timing configuration
  fastTiming: {
    duration: 150,
    useNativeDriver: true,
  },

  // Standard timing configuration
  standardTiming: {
    duration: 300,
    useNativeDriver: true,
  },

  // Slow timing configuration
  slowTiming: {
    duration: 500,
    useNativeDriver: true,
  },

  // Create optimized scale animation
  createScaleAnimation: (animatedValue, toValue, config = {}) => {
    return {
      transform: [
        {
          scale: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [0.8, toValue || 1],
            extrapolate: 'clamp',
          }),
        },
      ],
      ...config,
    };
  },

  // Create optimized opacity animation
  createOpacityAnimation: (animatedValue, toValue = 1) => {
    return {
      opacity: animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0, toValue],
        extrapolate: 'clamp',
      }),
    };
  },

  // Create optimized translate animation
  createTranslateAnimation: (animatedValue, distance = 50, direction = 'y') => {
    const transform = {};
    transform[`translate${direction.toUpperCase()}`] = animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [distance, 0],
      extrapolate: 'clamp',
    });

    return { transform: [transform] };
  },
};

// Component optimization hooks and utilities
export const OptimizationUtils = {
  // Memoization helper for expensive calculations
  memoize: (fn) => {
    const cache = new Map();
    return (...args) => {
      const key = JSON.stringify(args);
      if (cache.has(key)) {
        return cache.get(key);
      }
      const result = fn(...args);
      cache.set(key, result);
      return result;
    };
  },

  // Shallow comparison for React.memo
  shallowEqual: (prevProps, nextProps) => {
    const keys1 = Object.keys(prevProps);
    const keys2 = Object.keys(nextProps);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (let key of keys1) {
      if (prevProps[key] !== nextProps[key]) {
        return false;
      }
    }

    return true;
  },

  // Create optimized FlatList props
  createOptimizedFlatListProps: (itemHeight) => ({
    removeClippedSubviews: true,
    maxToRenderPerBatch: 10,
    updateCellsBatchingPeriod: 50,
    initialNumToRender: 10,
    windowSize: 10,
    getItemLayout: itemHeight ? (data, index) => ({
      length: itemHeight,
      offset: itemHeight * index,
      index,
    }) : undefined,
  }),

  // Image loading optimization
  optimizeImageProps: {
    progressiveRenderingEnabled: true,
    fadeDuration: 200,
    resizeMode: 'cover',
  },
};
