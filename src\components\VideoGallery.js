import React, { useRef, useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
  PanGestureHandler,
  GestureHandlerRootView,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors, Typography, Spacing } from '../constants';
import { videoService } from '../services';

const { width: screenWidth } = Dimensions.get('window');
const VIDEO_CARD_WIDTH = screenWidth * 0.7;
const VIDEO_CARD_HEIGHT = 200;

const VideoGallery = () => {
  const flatListRef = useRef(null);
  const autoScrollTimer = useRef(null);
  const userInteractionTimer = useRef(null);
  const lastScrollTime = useRef(0);
  const isMounted = useRef(true);

  // State management
  const [videos, setVideos] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isLayoutReady, setIsLayoutReady] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Configuration constants
  const AUTO_SCROLL_INTERVAL = 4500; // 4.5 seconds
  const USER_INTERACTION_PAUSE = 3000; // 3 seconds
  const SWIPE_THRESHOLD = 50; // 50px minimum swipe distance
  const INFINITE_LOOP = true; // Enable infinite loop

  // Fetch featured videos from API
  const fetchVideos = useCallback(async (forceRefresh = false) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await videoService.getFeaturedVideos({
        limit: 10,
        forceRefresh,
      });

      if (response && response.videos) {
        safeSetState(setVideos, response.videos);

        // Reset to first video if we have videos
        if (response.videos.length > 0) {
          safeSetState(setCurrentIndex, 0);
        }
      } else {
        throw new Error('No videos received from API');
      }

    } catch (error) {
      console.error('Error fetching videos:', error);
      setError(error.message || 'Failed to load videos');

      // Don't set hasError here, let component continue with empty state
      // setHasError(true);

    } finally {
      setIsLoading(false);
    }
  }, [safeSetState]);

  // Load videos on component mount
  useEffect(() => {
    fetchVideos();
  }, [fetchVideos]);

  // Safe state update function to prevent updates on unmounted components
  const safeSetState = useCallback((setter, value) => {
    if (isMounted.current && !hasError) {
      try {
        setter(value);
      } catch (error) {
        console.error('State update failed:', error);
        setHasError(true);
      }
    }
  }, [hasError]);

  // Safe ref operation function
  const safeRefOperation = useCallback((operation) => {
    if (isMounted.current && flatListRef.current) {
      try {
        return operation(flatListRef.current);
      } catch (error) {
        console.log('Ref operation failed:', error);
        return null;
      }
    }
    return null;
  }, []);

  // Safe timer cleanup
  const safeTimerCleanup = useCallback(() => {
    if (autoScrollTimer.current) {
      clearTimeout(autoScrollTimer.current);
      autoScrollTimer.current = null;
    }
    if (userInteractionTimer.current) {
      clearTimeout(userInteractionTimer.current);
      userInteractionTimer.current = null;
    }
  }, []);

  const handleVideoPress = async (video) => {
    try {
      // Record video view
      await videoService.recordVideoView(video.id);

      Alert.alert(
        video.title,
        `Durasi: ${video.duration}\nKategori: ${video.category}\nDilihat: ${video.views} kali`,
        [
          {
            text: 'Batal',
            style: 'cancel',
          },
          {
            text: 'Putar Video',
            onPress: () => {
              console.log('Play video:', video.title);
              // Video player logic would go here
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error handling video press:', error);
      Alert.alert('Error', 'Terjadi kesalahan saat memuat video');
    }
  };

  const handleViewAll = () => {
    console.log('View all videos');
    // Navigate to full video gallery
  };

  const formatViews = (views) => {
    if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}k`;
    }
    return views.toString();
  };

  // Auto-scroll functionality
  const startAutoScroll = useCallback(() => {
    if (!isMounted.current || !isAutoScrollEnabled || isUserInteracting || isDragging) return;

    safeTimerCleanup();

    if (isMounted.current) {
      autoScrollTimer.current = setTimeout(() => {
        if (!isMounted.current || isUserInteracting || isDragging || !isAutoScrollEnabled) return;

        let nextIndex;
        if (INFINITE_LOOP) {
          nextIndex = currentIndex >= videos.length - 1 ? 0 : currentIndex + 1;
        } else {
          nextIndex = Math.min(currentIndex + 1, videos.length - 1);
        }

        if (nextIndex !== currentIndex && nextIndex >= 0 && nextIndex < videos.length) {
          const success = safeRefOperation((ref) => {
            try {
              ref.scrollToIndex({
                index: nextIndex,
                animated: true,
                viewPosition: 0,
              });
              return true;
            } catch (error) {
              console.log('ScrollToIndex failed, using fallback:', error);
              // Fallback to offset scroll
              const cardWidth = VIDEO_CARD_WIDTH + Spacing.margin.md;
              const offset = nextIndex * cardWidth;
              ref.scrollToOffset({
                offset,
                animated: true
              });
              return true;
            }
          });

          if (success) {
            safeSetState(setCurrentIndex, nextIndex);
          }
        }
      }, AUTO_SCROLL_INTERVAL);
    }
  }, [currentIndex, isAutoScrollEnabled, isUserInteracting, isDragging, safeRefOperation, safeSetState, safeTimerCleanup]);

  const pauseAutoScroll = useCallback(() => {
    if (!isMounted.current) return;

    safeSetState(setIsUserInteracting, true);
    safeTimerCleanup();

    if (isMounted.current) {
      userInteractionTimer.current = setTimeout(() => {
        if (isMounted.current) {
          safeSetState(setIsUserInteracting, false);
        }
      }, USER_INTERACTION_PAUSE);
    }
  }, [safeSetState, safeTimerCleanup]);

  const scrollToIndex = useCallback((index, animated = true) => {
    if (!isMounted.current || index < 0 || index >= videos.length) return;

    const success = safeRefOperation((ref) => {
      try {
        ref.scrollToIndex({
          index,
          animated,
          viewPosition: 0,
        });
        return true;
      } catch (error) {
        console.log('ScrollToIndex failed, using scrollToOffset fallback:', error);
        // Fallback to scrollToOffset
        const cardWidth = VIDEO_CARD_WIDTH + Spacing.margin.md;
        const offset = index * cardWidth;
        ref.scrollToOffset({
          offset,
          animated
        });
        return true;
      }
    });

    if (success) {
      safeSetState(setCurrentIndex, index);
    }
  }, [safeRefOperation, safeSetState]);

  // Auto-scroll effect
  useEffect(() => {
    if (isMounted.current && isLayoutReady && isAutoScrollEnabled && !isUserInteracting && !isDragging) {
      startAutoScroll();
    }

    return () => {
      safeTimerCleanup();
    };
  }, [startAutoScroll, isAutoScrollEnabled, isUserInteracting, isDragging, isLayoutReady, safeTimerCleanup]);

  // Component lifecycle management
  useEffect(() => {
    isMounted.current = true;

    return () => {
      isMounted.current = false;
      try {
        safeTimerCleanup();
      } catch (error) {
        console.error('Cleanup failed:', error);
      }
    };
  }, [safeTimerCleanup]);

  // Error recovery effect
  useEffect(() => {
    if (hasError) {
      // Auto-recover after 5 seconds
      const recoveryTimer = setTimeout(() => {
        if (isMounted.current) {
          setHasError(false);
        }
      }, 5000);

      return () => clearTimeout(recoveryTimer);
    }
  }, [hasError]);

  const scrollToNext = useCallback(() => {
    if (!isMounted.current) return;

    pauseAutoScroll();
    let nextIndex;

    if (INFINITE_LOOP) {
      nextIndex = currentIndex >= videos.length - 1 ? 0 : currentIndex + 1;
    } else {
      nextIndex = Math.min(currentIndex + 1, videos.length - 1);
    }

    if (nextIndex !== currentIndex) {
      scrollToIndex(nextIndex, true);
    }
  }, [currentIndex, pauseAutoScroll, scrollToIndex]);

  const scrollToPrevious = useCallback(() => {
    if (!isMounted.current) return;

    pauseAutoScroll();
    let prevIndex;

    if (INFINITE_LOOP) {
      prevIndex = currentIndex <= 0 ? videos.length - 1 : currentIndex - 1;
    } else {
      prevIndex = Math.max(currentIndex - 1, 0);
    }

    if (prevIndex !== currentIndex) {
      scrollToIndex(prevIndex, true);
    }
  }, [currentIndex, pauseAutoScroll, scrollToIndex]);

  // Enhanced scroll handler with momentum and snap detection
  const onScroll = useCallback((event) => {
    if (!isMounted.current) return;

    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const cardWidth = VIDEO_CARD_WIDTH + Spacing.margin.md;
    const index = Math.round(contentOffsetX / cardWidth);

    // Update current index if it changed
    if (index !== currentIndex && index >= 0 && index < videos.length) {
      safeSetState(setCurrentIndex, index);
    }

    // Track scroll time for momentum detection
    lastScrollTime.current = Date.now();
  }, [currentIndex, safeSetState]);

  // Handle scroll begin (user starts touching)
  const onScrollBeginDrag = useCallback(() => {
    if (!isMounted.current) return;

    safeSetState(setIsDragging, true);
    pauseAutoScroll();
  }, [pauseAutoScroll, safeSetState]);

  // Handle scroll end (user releases touch)
  const onScrollEndDrag = useCallback(() => {
    if (!isMounted.current) return;

    safeSetState(setIsDragging, false);
  }, [safeSetState]);

  // Handle momentum scroll end (final position reached)
  const onMomentumScrollEnd = useCallback((event) => {
    if (!isMounted.current) return;

    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const cardWidth = VIDEO_CARD_WIDTH + Spacing.margin.md;
    const index = Math.round(contentOffsetX / cardWidth);

    // Ensure we're at the correct index
    if (index !== currentIndex && index >= 0 && index < videos.length) {
      safeSetState(setCurrentIndex, index);
    }

    safeSetState(setIsDragging, false);
  }, [currentIndex, safeSetState]);

  const renderVideoItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.videoCard}
        onPress={() => handleVideoPress(item)}
        activeOpacity={0.9}
      >
        <View style={styles.thumbnailContainer}>
          <Image source={{ uri: item.thumbnail }} style={styles.thumbnail} resizeMode="cover" />
          <View style={styles.playButton}>
            <MaterialIcons
              name="play-arrow"
              size={32}
              color={Colors.onPrimary}
            />
          </View>
          <View style={styles.durationBadge}>
            <Text style={styles.durationText}>{item.duration}</Text>
          </View>
        </View>
        
        <View style={styles.videoInfo}>
          <Text style={styles.videoTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <View style={styles.videoMeta}>
            <View style={styles.categoryBadge}>
              <Text style={styles.categoryText}>{item.category}</Text>
            </View>
            <View style={styles.metaInfo}>
              <MaterialIcons
                name="visibility"
                size={14}
                color={Colors.onSurfaceVariant}
              />
              <Text style={styles.viewsText}>{formatViews(item.views)}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.titleContainer}>
        <Text style={styles.sectionTitle}>Featured Videos</Text>
        <Text style={styles.sectionSubtitle}>
          Video pelatihan dan update terbaru
        </Text>
      </View>
      <TouchableOpacity onPress={handleViewAll} style={styles.viewAllButton}>
        <Text style={styles.viewAllText}>Lihat Semua</Text>
        <MaterialIcons
          name="arrow-forward"
          size={16}
          color={Colors.primary}
        />
      </TouchableOpacity>
    </View>
  );

  const renderNavigationControls = () => {
    const isPrevDisabled = !INFINITE_LOOP && currentIndex === 0;
    const isNextDisabled = !INFINITE_LOOP && currentIndex === videos.length - 1;

    return (
      <View style={styles.navigationContainer}>
        {/* Pagination Dots - Center */}
        <View style={styles.paginationContainer}>
          {videos.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.paginationDot,
                index === currentIndex && styles.paginationDotActive
              ]}
              onPress={() => {
                pauseAutoScroll();
                scrollToIndex(index, true);
              }}
              activeOpacity={0.7}
            />
          ))}
        </View>

        {/* Auto-scroll indicator */}
        <View style={styles.autoScrollIndicator}>
          <TouchableOpacity
            style={[
              styles.autoScrollButton,
              isAutoScrollEnabled && styles.autoScrollButtonActive
            ]}
            onPress={() => {
              if (isMounted.current) {
                safeSetState(setIsAutoScrollEnabled, !isAutoScrollEnabled);
                if (!isAutoScrollEnabled) {
                  safeSetState(setIsUserInteracting, false);
                }
              }
            }}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name={isAutoScrollEnabled ? "pause" : "play-arrow"}
              size={16}
              color={isAutoScrollEnabled ? Colors.primary : Colors.onSurfaceVariant}
            />
          </TouchableOpacity>
        </View>

        {/* Navigation Buttons - Right Side */}
        <View style={styles.navigationButtons}>
          <TouchableOpacity
            style={[styles.navButton, isPrevDisabled && styles.navButtonDisabled]}
            onPress={scrollToPrevious}
            disabled={isPrevDisabled}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name="chevron-left"
              size={20}
              color={isPrevDisabled ? Colors.onSurfaceVariant : Colors.primary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, isNextDisabled && styles.navButtonDisabled]}
            onPress={scrollToNext}
            disabled={isNextDisabled}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name="chevron-right"
              size={20}
              color={isNextDisabled ? Colors.onSurfaceVariant : Colors.primary}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Don't render if component is unmounted or has error
  if (!isMounted.current || hasError) {
    return hasError ? (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Video gallery temporarily unavailable</Text>
      </View>
    ) : null;
  }

  // Loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading videos...</Text>
        </View>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={48} color={Colors.error} />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => fetchVideos(true)}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Empty state
  if (!videos || videos.length === 0) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <View style={styles.emptyContainer}>
          <MaterialIcons name="video-library" size={48} color={Colors.onSurfaceVariant} />
          <Text style={styles.emptyText}>No videos available</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => fetchVideos(true)}
          >
            <Text style={styles.retryButtonText}>Refresh</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {renderHeader()}

      <View style={styles.galleryContainer}>
        <FlatList
          ref={flatListRef}
          data={videos}
          renderItem={renderVideoItem}
          keyExtractor={(item) => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}

          // Touch-based scrolling configuration
          snapToInterval={VIDEO_CARD_WIDTH + Spacing.margin.md}
          snapToAlignment="start"
          decelerationRate="fast"

          // Scroll event handlers
          onScroll={onScroll}
          scrollEventThrottle={16} // 60fps performance
          onScrollBeginDrag={onScrollBeginDrag}
          onScrollEndDrag={onScrollEndDrag}
          onMomentumScrollEnd={onMomentumScrollEnd}

          // Touch interaction settings
          scrollEnabled={true}
          bounces={false}
          pagingEnabled={false} // Use snapToInterval instead

          // Performance optimizations
          removeClippedSubviews={false} // Disable to prevent scroll issues
          maxToRenderPerBatch={videos.length} // Render all items
          windowSize={10}
          initialNumToRender={videos.length} // Render all initially
          getItemLayout={(data, index) => ({
            length: VIDEO_CARD_WIDTH + Spacing.margin.md,
            offset: (VIDEO_CARD_WIDTH + Spacing.margin.md) * index,
            index,
          })}

          // Error handling
          onScrollToIndexFailed={(info) => {
            console.log('Scroll to index failed:', info);
            // Wait for layout then retry
            setTimeout(() => {
              if (isMounted.current) {
                safeRefOperation((ref) => {
                  const offset = info.index * (VIDEO_CARD_WIDTH + Spacing.margin.md);
                  ref.scrollToOffset({
                    offset,
                    animated: true
                  });
                  return true;
                });
                safeSetState(setCurrentIndex, info.index);
              }
            }, 100);
          }}

          // Layout handling
          onLayout={() => {
            if (isMounted.current && !isLayoutReady) {
              safeSetState(setIsLayoutReady, true);
            }
          }}

          // Accessibility
          accessible={true}
          accessibilityLabel="Featured videos gallery"
          accessibilityHint="Swipe left or right to browse videos"
        />
      </View>

      {renderNavigationControls()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: Spacing.padding.sm,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: Spacing.padding.md,
    marginBottom: Spacing.md,
  },
  titleContainer: {
    flex: 1,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.xs,
  },
  viewAllText: {
    ...Typography.body2,
    color: Colors.primary,
    marginRight: Spacing.xs,
    fontWeight: '500',
  },
  listContainer: {
    paddingHorizontal: Spacing.padding.md,
  },
  galleryContainer: {
    position: 'relative',
  },
  videoCard: {
    width: VIDEO_CARD_WIDTH,
    marginRight: Spacing.margin.md,
    backgroundColor: Colors.surface,
    borderRadius: Spacing.borderRadius.lg,
    overflow: 'hidden',
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  thumbnailContainer: {
    position: 'relative',
    height: VIDEO_CARD_HEIGHT * 0.6,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  durationBadge: {
    position: 'absolute',
    bottom: Spacing.xs,
    right: Spacing.xs,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.borderRadius.sm,
  },
  durationText: {
    ...Typography.caption,
    color: Colors.onPrimary,
    fontSize: 10,
  },
  videoInfo: {
    padding: Spacing.padding.md,
  },
  videoTitle: {
    ...Typography.subtitle2,
    color: Colors.onSurface,
    marginBottom: Spacing.sm,
  },
  videoMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryBadge: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: Spacing.xs,
    paddingVertical: 2,
    borderRadius: Spacing.borderRadius.sm,
  },
  categoryText: {
    ...Typography.caption,
    color: Colors.primary,
    fontSize: 10,
    fontWeight: '500',
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewsText: {
    ...Typography.caption,
    color: Colors.onSurfaceVariant,
    marginLeft: Spacing.xs / 2,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.padding.md,
    paddingVertical: Spacing.sm,
    position: 'relative',
  },
  paginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  paginationDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.outlineVariant,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: Colors.primary,
    width: 16,
    borderRadius: 3,
  },
  navigationButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    right: Spacing.padding.md,
  },
  navButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 2,
    borderWidth: 1,
    borderColor: Colors.outline,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 1,
  },
  navButtonDisabled: {
    backgroundColor: Colors.surfaceVariant,
    borderColor: Colors.outlineVariant,
    shadowOpacity: 0,
    elevation: 0,
  },
  autoScrollIndicator: {
    position: 'absolute',
    left: Spacing.padding.md,
    top: '50%',
    transform: [{ translateY: -14 }],
  },
  autoScrollButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: Colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.outlineVariant,
  },
  autoScrollButtonActive: {
    backgroundColor: Colors.surface,
    borderColor: Colors.primary,
    shadowColor: Colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 1,
  },
  loadingContainer: {
    padding: Spacing.padding.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: VIDEO_CARD_HEIGHT,
  },
  loadingText: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
    marginTop: Spacing.sm,
    textAlign: 'center',
  },
  errorContainer: {
    padding: Spacing.padding.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: VIDEO_CARD_HEIGHT,
  },
  errorText: {
    ...Typography.body2,
    color: Colors.error,
    textAlign: 'center',
    marginTop: Spacing.sm,
    marginBottom: Spacing.md,
  },
  emptyContainer: {
    padding: Spacing.padding.lg,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: VIDEO_CARD_HEIGHT,
  },
  emptyText: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: Spacing.sm,
    marginBottom: Spacing.md,
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.padding.md,
    paddingVertical: Spacing.padding.sm,
    borderRadius: 8,
  },
  retryButtonText: {
    ...Typography.button,
    color: Colors.onPrimary,
  },
});

export default VideoGallery;
