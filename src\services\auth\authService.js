/**
 * Authentication Service
 * Handles user authentication, token management, and session persistence
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiClient, ApiError } from '../api/apiClient';

// Storage keys
const STORAGE_KEYS = {
  ACCESS_TOKEN: '@psg_bmi_access_token',
  REFRESH_TOKEN: '@psg_bmi_refresh_token',
  USER_DATA: '@psg_bmi_user_data',
  REMEMBER_ME: '@psg_bmi_remember_me',
};

class AuthService {
  constructor() {
    this.currentUser = null;
    this.isAuthenticated = false;
    this.authListeners = [];
  }

  // Authentication state listeners
  addAuthListener(callback) {
    this.authListeners.push(callback);
    return () => {
      this.authListeners = this.authListeners.filter(listener => listener !== callback);
    };
  }

  notifyAuthListeners() {
    this.authListeners.forEach(callback => {
      callback({
        isAuthenticated: this.isAuthenticated,
        user: this.currentUser,
      });
    });
  }

  // Login with username/password
  async login(credentials) {
    try {
      const { username, password, rememberMe = false } = credentials;

      // Validate input
      if (!username || !password) {
        throw new ApiError('Username and password are required', 400);
      }

      // For development - mock authentication
      if (__DEV__ && username === 'admin' && password === 'password') {
        const mockResponse = await this.mockLogin(credentials);
        return mockResponse;
      }

      // Real API call
      const response = await apiClient.post('/auth/login', {
        username,
        password,
      });

      const { data } = response;
      const { accessToken, refreshToken, user } = data;

      // Store tokens and user data
      await this.storeAuthData({
        accessToken,
        refreshToken,
        user,
        rememberMe,
      });

      // Update client state
      this.currentUser = user;
      this.isAuthenticated = true;
      apiClient.setAuthToken(accessToken);

      // Notify listeners
      this.notifyAuthListeners();

      return {
        success: true,
        user,
        message: 'Login successful',
      };

    } catch (error) {
      console.error('Login error:', error);
      throw new ApiError(
        error.message || 'Login failed',
        error.status || 500,
        error.data
      );
    }
  }

  // Mock login for development
  async mockLogin(credentials) {
    const { username, rememberMe = false } = credentials;
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const mockUser = {
      id: '1',
      username,
      name: 'Administrator',
      email: '<EMAIL>',
      role: 'admin',
      department: 'IT',
      avatar: null,
      permissions: ['read', 'write', 'admin'],
    };

    const mockTokens = {
      accessToken: 'mock_access_token_' + Date.now(),
      refreshToken: 'mock_refresh_token_' + Date.now(),
    };

    // Store mock data
    await this.storeAuthData({
      accessToken: mockTokens.accessToken,
      refreshToken: mockTokens.refreshToken,
      user: mockUser,
      rememberMe,
    });

    // Update client state
    this.currentUser = mockUser;
    this.isAuthenticated = true;
    apiClient.setAuthToken(mockTokens.accessToken);

    // Notify listeners
    this.notifyAuthListeners();

    return {
      success: true,
      user: mockUser,
      message: 'Login successful',
    };
  }

  // Logout
  async logout() {
    try {
      // Call logout API if authenticated
      if (this.isAuthenticated) {
        try {
          await apiClient.post('/auth/logout');
        } catch (error) {
          console.warn('Logout API call failed:', error);
          // Continue with local logout even if API fails
        }
      }

      // Clear stored data
      await this.clearAuthData();

      // Update client state
      this.currentUser = null;
      this.isAuthenticated = false;
      apiClient.setAuthToken(null);

      // Notify listeners
      this.notifyAuthListeners();

      return {
        success: true,
        message: 'Logout successful',
      };

    } catch (error) {
      console.error('Logout error:', error);
      throw new ApiError(
        error.message || 'Logout failed',
        error.status || 500
      );
    }
  }

  // Refresh access token
  async refreshToken() {
    try {
      const refreshToken = await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      
      if (!refreshToken) {
        throw new ApiError('No refresh token available', 401);
      }

      // For development - mock refresh
      if (__DEV__ && refreshToken.startsWith('mock_refresh_token_')) {
        return await this.mockRefreshToken();
      }

      // Real API call
      const response = await apiClient.post('/auth/refresh', {
        refreshToken,
      });

      const { data } = response;
      const { accessToken, refreshToken: newRefreshToken } = data;

      // Update stored tokens
      await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
      if (newRefreshToken) {
        await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken);
      }

      // Update API client
      apiClient.setAuthToken(accessToken);

      return {
        success: true,
        accessToken,
      };

    } catch (error) {
      console.error('Token refresh error:', error);
      // If refresh fails, logout user
      await this.logout();
      throw error;
    }
  }

  // Mock token refresh for development
  async mockRefreshToken() {
    await new Promise(resolve => setTimeout(resolve, 500));

    const newAccessToken = 'mock_access_token_' + Date.now();
    
    await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, newAccessToken);
    apiClient.setAuthToken(newAccessToken);

    return {
      success: true,
      accessToken: newAccessToken,
    };
  }

  // Store authentication data
  async storeAuthData({ accessToken, refreshToken, user, rememberMe }) {
    try {
      await AsyncStorage.multiSet([
        [STORAGE_KEYS.ACCESS_TOKEN, accessToken],
        [STORAGE_KEYS.REFRESH_TOKEN, refreshToken],
        [STORAGE_KEYS.USER_DATA, JSON.stringify(user)],
        [STORAGE_KEYS.REMEMBER_ME, JSON.stringify(rememberMe)],
      ]);
    } catch (error) {
      console.error('Error storing auth data:', error);
      throw new ApiError('Failed to store authentication data', 500);
    }
  }

  // Clear authentication data
  async clearAuthData() {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.ACCESS_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.REMEMBER_ME,
      ]);
    } catch (error) {
      console.error('Error clearing auth data:', error);
    }
  }

  // Restore authentication state from storage
  async restoreAuthState() {
    try {
      const [accessToken, refreshToken, userData, rememberMe] = await AsyncStorage.multiGet([
        STORAGE_KEYS.ACCESS_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.REMEMBER_ME,
      ]);

      const token = accessToken[1];
      const user = userData[1] ? JSON.parse(userData[1]) : null;
      const shouldRemember = rememberMe[1] ? JSON.parse(rememberMe[1]) : false;

      if (token && user && shouldRemember) {
        // Set auth state
        this.currentUser = user;
        this.isAuthenticated = true;
        apiClient.setAuthToken(token);

        // Try to refresh token to ensure it's still valid
        try {
          await this.refreshToken();
        } catch (error) {
          console.warn('Token refresh failed during restore:', error);
          await this.logout();
          return false;
        }

        // Notify listeners
        this.notifyAuthListeners();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error restoring auth state:', error);
      return false;
    }
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Check if user is authenticated
  getIsAuthenticated() {
    return this.isAuthenticated;
  }

  // Check user permissions
  hasPermission(permission) {
    if (!this.currentUser || !this.currentUser.permissions) {
      return false;
    }
    return this.currentUser.permissions.includes(permission);
  }

  // Check user role
  hasRole(role) {
    if (!this.currentUser) {
      return false;
    }
    return this.currentUser.role === role;
  }
}

// Create singleton instance
const authService = new AuthService();

export { authService, STORAGE_KEYS };
export default authService;
