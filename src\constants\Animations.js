// Animation constants for consistent micro-interactions
export const Animations = {
  // Duration constants
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
  },
  
  // Easing curves
  easing: {
    easeInOut: 'ease-in-out',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    linear: 'linear',
  },
  
  // Scale animations
  scale: {
    pressed: 0.95,
    normal: 1.0,
    hover: 1.02,
  },
  
  // Opacity animations
  opacity: {
    hidden: 0,
    visible: 1,
    disabled: 0.6,
  },
  
  // Spring configurations
  spring: {
    default: {
      damping: 15,
      stiffness: 150,
      mass: 1,
    },
    gentle: {
      damping: 20,
      stiffness: 100,
      mass: 1,
    },
    bouncy: {
      damping: 10,
      stiffness: 200,
      mass: 1,
    },
  },
  
  // Timing configurations
  timing: {
    fast: {
      duration: 150,
      useNativeDriver: true,
    },
    normal: {
      duration: 300,
      useNativeDriver: true,
    },
    slow: {
      duration: 500,
      useNativeDriver: true,
    },
  },
};
