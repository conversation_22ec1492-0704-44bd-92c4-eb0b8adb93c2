import React, { createContext, useContext, useState, useEffect } from 'react';
import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import * as LocalAuthentication from 'expo-local-authentication';
import { authService } from '../services';

// Create Auth Context
const AuthContext = createContext({});

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [biometricSupported, setBiometricSupported] = useState(false);
  const [biometricEnabled, setBiometricEnabled] = useState(false);

  // Storage keys
  const TOKEN_KEY = 'auth_token';
  const USER_KEY = 'auth_user';
  const BIOMETRIC_KEY = 'biometric_enabled';

  // Cross-platform storage utility
  const storage = {
    async getItem(key) {
      if (Platform.OS === 'web') {
        return localStorage.getItem(key);
      }
      return await SecureStore.getItemAsync(key);
    },

    async setItem(key, value) {
      if (Platform.OS === 'web') {
        localStorage.setItem(key, value);
        return;
      }
      return await SecureStore.setItemAsync(key, value);
    },

    async removeItem(key) {
      if (Platform.OS === 'web') {
        localStorage.removeItem(key);
        return;
      }
      return await SecureStore.deleteItemAsync(key);
    }
  };

  // Check biometric support and restore auth state on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);

      // Check biometric support
      await checkBiometricSupport();

      // Restore authentication state using authService
      const isRestored = await authService.restoreAuthState();

      if (isRestored) {
        const currentUser = authService.getCurrentUser();
        const isAuthenticated = authService.getIsAuthenticated();

        setUser(currentUser);
        setIsAuthenticated(isAuthenticated);

        // Also set token for backward compatibility
        setToken('restored_token');
      }

    } catch (error) {
      console.error('Error initializing auth:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const checkBiometricSupport = async () => {
    try {
      const compatible = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      setBiometricSupported(compatible && enrolled);
      
      if (compatible && enrolled) {
        const biometricPref = await SecureStore.getItemAsync(BIOMETRIC_KEY);
        setBiometricEnabled(biometricPref === 'true');
      }
    } catch (error) {
      console.log('Biometric check error:', error);
      setBiometricSupported(false);
    }
  };



  const login = async (credentials) => {
    try {
      setIsLoading(true);

      // Use authService for login
      const response = await authService.login(credentials);

      if (response.success) {
        const currentUser = authService.getCurrentUser();
        const isAuthenticated = authService.getIsAuthenticated();

        setUser(currentUser);
        setIsAuthenticated(isAuthenticated);
        setToken('authenticated_token'); // For backward compatibility

        return { success: true, user: currentUser };
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithBiometric = async () => {
    try {
      if (!biometricSupported || !biometricEnabled) {
        throw new Error('Biometric authentication not available');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate with your biometric',
        fallbackLabel: 'Use password instead',
        cancelLabel: 'Cancel',
      });

      if (result.success) {
        // Restore auth state using authService
        const isRestored = await authService.restoreAuthState();

        if (isRestored) {
          const currentUser = authService.getCurrentUser();
          setUser(currentUser);
          setIsAuthenticated(true);
          setToken('biometric_token');
          return { success: true };
        } else {
          throw new Error('No stored credentials found');
        }
      } else {
        throw new Error('Biometric authentication failed');
      }
    } catch (error) {
      console.error('Biometric login error:', error);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);

      // Use authService for logout
      const response = await authService.logout();

      // Reset local state
      setToken(null);
      setUser(null);
      setIsAuthenticated(false);

      return response;
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  const clearAuth = async () => {
    try {
      // Use authService to clear auth data
      await authService.clearAuthData();

      setToken(null);
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Clear auth error:', error);
    }
  };

  const enableBiometric = async (enable) => {
    try {
      if (!biometricSupported) {
        throw new Error('Biometric authentication not supported');
      }

      await SecureStore.setItemAsync(BIOMETRIC_KEY, enable.toString());
      setBiometricEnabled(enable);
      return { success: true };
    } catch (error) {
      console.log('Enable biometric error:', error);
      return { success: false, error: error.message };
    }
  };

  const refreshToken = async () => {
    try {
      // Use authService for token refresh
      const response = await authService.refreshToken();

      if (response.success) {
        setToken('refreshed_token'); // For backward compatibility
        return { success: true, token: response.accessToken };
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      await clearAuth();
      return { success: false, error: error.message };
    }
  };

  // Add auth service listener for state changes
  useEffect(() => {
    const unsubscribe = authService.addAuthListener((authState) => {
      setUser(authState.user);
      setIsAuthenticated(authState.isAuthenticated);

      if (authState.isAuthenticated) {
        setToken('service_token'); // For backward compatibility
      } else {
        setToken(null);
      }
    });

    return unsubscribe;
  }, []);

  const value = {
    // State
    user,
    token,
    isLoading,
    isAuthenticated,
    biometricSupported,
    biometricEnabled,
    
    // Methods
    login,
    loginWithBiometric,
    logout,
    enableBiometric,
    refreshToken,
    clearAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
