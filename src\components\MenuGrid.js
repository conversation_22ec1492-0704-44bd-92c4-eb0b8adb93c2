import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Dimensions,
  Alert,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';
import { MenuCard } from './';
import { MenuData } from '../constants/MenuData';

const { width: screenWidth } = Dimensions.get('window');
const GRID_PADDING = Spacing.padding.md;
const CARD_MARGIN = Spacing.margin.sm;
const CARDS_PER_ROW = 2;
const CARD_WIDTH = (screenWidth - (GRID_PADDING * 2) - (CARD_MARGIN * (CARDS_PER_ROW - 1))) / CARDS_PER_ROW;

const MenuGrid = () => {
  const handleMenuPress = (item) => {
    // For now, show an alert. In a real app, this would navigate to the respective screen
    Alert.alert(
      item.title,
      item.description,
      [
        {
          text: 'Batal',
          style: 'cancel',
        },
        {
          text: 'Buka Aplikasi',
          onPress: () => {
            console.log(`Navigate to ${item.route}`);
            // Navigation logic would go here
          },
        },
      ]
    );
  };

  const renderMenuItem = ({ item, index }) => {
    return (
      <View style={[styles.cardContainer, { width: CARD_WIDTH }]}>
        <MenuCard
          title={item.title}
          subtitle={item.subtitle}
          icon={item.icon}
          iconType={item.iconType}
          color={item.color}
          onPress={() => handleMenuPress(item)}
        />
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      <Text style={styles.sectionSubtitle}>
        Akses cepat ke aplikasi yang sering digunakan
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderHeader()}
      
      <FlatList
        data={MenuData}
        renderItem={renderMenuItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={CARDS_PER_ROW}
        scrollEnabled={false}
        contentContainerStyle={styles.gridContainer}
        columnWrapperStyle={styles.row}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: GRID_PADDING,
  },
  headerContainer: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
  },
  gridContainer: {
    paddingBottom: Spacing.sm,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: CARD_MARGIN * 2,
  },
  cardContainer: {
    marginBottom: CARD_MARGIN,
  },
});

export default MenuGrid;
