// Accessibility utilities and helpers
import { AccessibilityInfo } from 'react-native';

export const AccessibilityUtils = {
  // Check if screen reader is enabled
  isScreenReaderEnabled: async () => {
    try {
      return await AccessibilityInfo.isScreenReaderEnabled();
    } catch (error) {
      console.warn('Error checking screen reader status:', error);
      return false;
    }
  },

  // Announce message to screen reader
  announceForAccessibility: (message) => {
    AccessibilityInfo.announceForAccessibility(message);
  },

  // Generate accessibility label for buttons
  generateButtonLabel: (title, description, state) => {
    let label = title;
    if (description) {
      label += `, ${description}`;
    }
    if (state) {
      label += `, ${state}`;
    }
    return label;
  },

  // Generate accessibility label for cards
  generateCardLabel: (title, subtitle, additionalInfo) => {
    let label = `Card, ${title}`;
    if (subtitle) {
      label += `, ${subtitle}`;
    }
    if (additionalInfo) {
      label += `, ${additionalInfo}`;
    }
    return label;
  },

  // Generate accessibility label for statistics
  generateStatLabel: (title, value, subtitle, trend) => {
    let label = `Statistik, ${title}, ${value}`;
    if (subtitle) {
      label += `, ${subtitle}`;
    }
    if (trend) {
      const trendText = trend === 'positive' ? 'meningkat' : 
                       trend === 'negative' ? 'menurun' : 'stabil';
      label += `, tren ${trendText}`;
    }
    return label;
  },

  // Common accessibility roles
  roles: {
    button: 'button',
    link: 'link',
    text: 'text',
    image: 'image',
    header: 'header',
    summary: 'summary',
    adjustable: 'adjustable',
    search: 'search',
    tab: 'tab',
    tablist: 'tablist',
    menu: 'menu',
    menuitem: 'menuitem',
  },

  // Common accessibility states
  states: {
    disabled: { disabled: true },
    selected: { selected: true },
    expanded: { expanded: true },
    collapsed: { expanded: false },
    checked: { checked: true },
    unchecked: { checked: false },
    busy: { busy: true },
  },

  // Common accessibility traits (iOS)
  traits: {
    button: 'button',
    link: 'link',
    header: 'header',
    search: 'search',
    image: 'image',
    selected: 'selected',
    plays: 'playsSound',
    key: 'keyboardKey',
    text: 'staticText',
    summary: 'summary',
    disabled: 'notEnabled',
    frequentUpdates: 'updatesFrequently',
    startsMedia: 'startsMediaSession',
    adjustable: 'adjustable',
    allowsDirectInteraction: 'allowsDirectInteraction',
    causesPageTurn: 'causesPageTurn',
    tabBar: 'tabBar',
  },
};

// Performance optimization utilities
export const PerformanceUtils = {
  // Debounce function for performance optimization
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // Throttle function for scroll events
  throttle: (func, limit) => {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },

  // Optimize FlatList performance
  flatListOptimizations: {
    removeClippedSubviews: true,
    maxToRenderPerBatch: 10,
    updateCellsBatchingPeriod: 50,
    initialNumToRender: 10,
    windowSize: 10,
    getItemLayout: (data, index, itemHeight) => ({
      length: itemHeight,
      offset: itemHeight * index,
      index,
    }),
  },

  // Image optimization settings
  imageOptimizations: {
    resizeMode: 'cover',
    fadeDuration: 300,
    progressiveRenderingEnabled: true,
    borderRadius: 0, // Set to 0 for better performance
  },
};
