import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  Dimensions,
  Alert,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';
import { StatCard } from './';
import { DashboardStats } from '../constants/MenuData';

const { width: screenWidth } = Dimensions.get('window');
const GRID_PADDING = Spacing.padding.md;
const CARD_MARGIN = Spacing.margin.sm;
const CARDS_PER_ROW = 2;
const CARD_WIDTH = (screenWidth - (GRID_PADDING * 2) - (CARD_MARGIN * (CARDS_PER_ROW - 1))) / CARDS_PER_ROW;

const DashboardOverview = () => {
  const handleStatPress = (item) => {
    Alert.alert(
      item.title,
      `Detail informasi untuk ${item.title.toLowerCase()}`,
      [
        {
          text: 'Tutup',
          style: 'cancel',
        },
        {
          text: 'Lihat Detail',
          onPress: () => {
            console.log(`View details for ${item.title}`);
            // Navigation to detail screen would go here
          },
        },
      ]
    );
  };

  const renderStatItem = ({ item, index }) => {
    return (
      <View style={[styles.cardContainer, { width: CARD_WIDTH }]}>
        <StatCard
          title={item.title}
          value={item.value}
          subtitle={item.subtitle}
          icon={item.icon}
          color={item.color}
          trend={item.trend}
          onPress={() => handleStatPress(item)}
        />
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.sectionTitle}>Today's Overview</Text>
      <Text style={styles.sectionSubtitle}>
        Ringkasan aktivitas dan statistik hari ini
      </Text>
    </View>
  );

  const getCurrentDate = () => {
    const today = new Date();
    const options = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return today.toLocaleDateString('id-ID', options);
  };

  return (
    <View style={styles.container}>
      {renderHeader()}
      
      <View style={styles.dateContainer}>
        <Text style={styles.dateText}>{getCurrentDate()}</Text>
      </View>
      
      <FlatList
        data={DashboardStats}
        renderItem={renderStatItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={CARDS_PER_ROW}
        scrollEnabled={false}
        contentContainerStyle={styles.gridContainer}
        columnWrapperStyle={styles.row}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: GRID_PADDING,
  },
  headerContainer: {
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
  },
  dateContainer: {
    backgroundColor: Colors.primaryLight,
    paddingHorizontal: Spacing.padding.md,
    paddingVertical: Spacing.padding.sm,
    borderRadius: Spacing.borderRadius.md,
    marginBottom: Spacing.lg,
  },
  dateText: {
    ...Typography.body2,
    color: Colors.primary,
    textAlign: 'center',
    fontWeight: '500',
  },
  gridContainer: {
    paddingBottom: Spacing.sm,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: CARD_MARGIN * 2,
  },
  cardContainer: {
    marginBottom: CARD_MARGIN,
  },
});

export default DashboardOverview;
