import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors, Typography, Spacing } from '../constants';

// Sample activities data
const activitiesData = [
  {
    id: 1,
    title: 'Check-in berhasil',
    description: 'Anda telah melakukan check-in pada pukul 07:45',
    time: '2 jam yang lalu',
    type: 'attendance',
    icon: 'schedule',
    color: Colors.success,
    status: 'completed',
  },
  {
    id: 2,
    title: 'Pengajuan cuti disetujui',
    description: 'Pengajuan cuti tanggal 25-27 Januari telah disetujui',
    time: '5 jam yang lalu',
    type: 'approval',
    icon: 'check-circle',
    color: Colors.success,
    status: 'approved',
  },
  {
    id: 3,
    title: 'Training Safety wajib',
    description: 'Anda memiliki training keselamatan kerja yang harus diselesaikan',
    time: '1 hari yang lalu',
    type: 'training',
    icon: 'school',
    color: Colors.warning,
    status: 'pending',
  },
  {
    id: 4,
    title: 'Laporan produksi dikirim',
    description: 'Laporan produksi harian telah berhasil dikirim',
    time: '2 hari yang lalu',
    type: 'report',
    icon: 'assignment',
    color: Colors.primary,
    status: 'completed',
  },
  {
    id: 5,
    title: 'Update sistem maintenance',
    description: 'Sistem CNM akan mengalami maintenance pada 20 Januari',
    time: '3 hari yang lalu',
    type: 'system',
    icon: 'info',
    color: Colors.info,
    status: 'info',
  },
];

const RecentActivities = () => {
  const handleActivityPress = (activity) => {
    Alert.alert(
      activity.title,
      activity.description,
      [
        {
          text: 'Tutup',
          style: 'cancel',
        },
        {
          text: 'Lihat Detail',
          onPress: () => {
            console.log('View activity details:', activity.title);
          },
        },
      ]
    );
  };

  const handleViewAll = () => {
    console.log('View all activities');
    // Navigate to full activities screen
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return Colors.success;
      case 'approved':
        return Colors.success;
      case 'pending':
        return Colors.warning;
      case 'info':
        return Colors.info;
      default:
        return Colors.onSurfaceVariant;
    }
  };

  const renderActivityItem = ({ item, index }) => {
    const isLast = index === activitiesData.length - 1;
    
    return (
      <TouchableOpacity
        style={styles.activityItem}
        onPress={() => handleActivityPress(item)}
        activeOpacity={0.8}
      >
        <View style={styles.timelineContainer}>
          <View style={[styles.iconContainer, { backgroundColor: `${item.color}15` }]}>
            <MaterialIcons
              name={item.icon}
              size={Spacing.iconSize.sm}
              color={item.color}
            />
          </View>
          {!isLast && <View style={styles.timelineLine} />}
        </View>
        
        <View style={styles.contentContainer}>
          <View style={styles.activityHeader}>
            <Text style={styles.activityTitle} numberOfLines={1}>
              {item.title}
            </Text>
            <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status) }]} />
          </View>
          
          <Text style={styles.activityDescription} numberOfLines={2}>
            {item.description}
          </Text>
          
          <Text style={styles.activityTime}>{item.time}</Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.titleContainer}>
        <Text style={styles.sectionTitle}>Recent Activities</Text>
        <Text style={styles.sectionSubtitle}>
          Aktivitas dan notifikasi terbaru
        </Text>
      </View>
      <TouchableOpacity onPress={handleViewAll} style={styles.viewAllButton}>
        <Text style={styles.viewAllText}>Lihat Semua</Text>
        <MaterialIcons
          name="arrow-forward"
          size={16}
          color={Colors.primary}
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {renderHeader()}
      
      <View style={styles.activitiesContainer}>
        <FlatList
          data={activitiesData}
          renderItem={renderActivityItem}
          keyExtractor={(item) => item.id.toString()}
          scrollEnabled={false}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.padding.md,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },
  titleContainer: {
    flex: 1,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.xs,
  },
  viewAllText: {
    ...Typography.body2,
    color: Colors.primary,
    marginRight: Spacing.xs,
    fontWeight: '500',
  },
  activitiesContainer: {
    backgroundColor: Colors.surface,
    borderRadius: Spacing.borderRadius.lg,
    padding: Spacing.padding.md,
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activityItem: {
    flexDirection: 'row',
    paddingVertical: Spacing.padding.sm,
  },
  timelineContainer: {
    alignItems: 'center',
    marginRight: Spacing.md,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: Colors.outlineVariant,
    marginTop: Spacing.xs,
  },
  contentContainer: {
    flex: 1,
    paddingBottom: Spacing.sm,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: Spacing.xs,
  },
  activityTitle: {
    ...Typography.subtitle2,
    color: Colors.onSurface,
    flex: 1,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: Spacing.xs,
  },
  activityDescription: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
    marginBottom: Spacing.xs,
    lineHeight: 20,
  },
  activityTime: {
    ...Typography.caption,
    color: Colors.onSurfaceVariant,
  },
});

export default RecentActivities;
