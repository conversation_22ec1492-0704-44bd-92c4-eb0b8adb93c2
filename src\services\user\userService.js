/**
 * User Service
 * Handles user profile, preferences, and activity management
 */

import { apiClient, ApiError } from '../api/apiClient';
import authService from '../auth/authService';

class UserService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 10 * 60 * 1000; // 10 minutes
  }

  // Get user profile
  async getUserProfile(userId = null) {
    try {
      const targetUserId = userId || authService.getCurrentUser()?.id;
      
      if (!targetUserId) {
        throw new ApiError('User ID is required', 400);
      }

      const cacheKey = `user_profile_${targetUserId}`;
      
      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockData = await this.getMockUserProfile(targetUserId);
        this.setCache(cacheKey, mockData);
        return mockData;
      }

      // Real API call
      const response = await apiClient.get(`/users/${targetUserId}/profile`);
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw new ApiError(
        error.message || 'Failed to fetch user profile',
        error.status || 500
      );
    }
  }

  // Get mock user profile for development
  async getMockUserProfile(userId) {
    await new Promise(resolve => setTimeout(resolve, 300));

    const currentUser = authService.getCurrentUser();
    
    return {
      id: userId,
      username: currentUser?.username || 'admin',
      name: currentUser?.name || 'Administrator',
      email: currentUser?.email || '<EMAIL>',
      phone: '+62 812-3456-7890',
      department: currentUser?.department || 'IT',
      position: 'System Administrator',
      employeeId: 'PSG001',
      joinDate: '2023-01-15',
      avatar: null,
      role: currentUser?.role || 'admin',
      permissions: currentUser?.permissions || ['read', 'write', 'admin'],
      preferences: {
        language: 'id',
        timezone: 'Asia/Jakarta',
        notifications: {
          email: true,
          push: true,
          sms: false,
        },
        theme: 'light',
        autoScroll: true,
      },
      stats: {
        loginCount: 156,
        lastLogin: new Date().toISOString(),
        videosWatched: 23,
        trainingCompleted: 8,
        pointsEarned: 1250,
      },
    };
  }

  // Update user profile
  async updateUserProfile(profileData) {
    try {
      const currentUser = authService.getCurrentUser();
      
      if (!currentUser) {
        throw new ApiError('User not authenticated', 401);
      }

      // For development - simulate update
      if (__DEV__) {
        const mockData = await this.mockUpdateUserProfile(profileData);
        
        // Clear cache to force refresh
        this.clearUserCache(currentUser.id);
        
        return mockData;
      }

      // Real API call
      const response = await apiClient.put(`/users/${currentUser.id}/profile`, profileData);
      const { data } = response;

      // Clear cache to force refresh
      this.clearUserCache(currentUser.id);

      return data;

    } catch (error) {
      console.error('Error updating user profile:', error);
      throw new ApiError(
        error.message || 'Failed to update user profile',
        error.status || 500
      );
    }
  }

  // Mock update user profile for development
  async mockUpdateUserProfile(profileData) {
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log('Mock updating user profile:', profileData);

    return {
      success: true,
      message: 'Profile updated successfully',
      updatedFields: Object.keys(profileData),
    };
  }

  // Get user preferences
  async getUserPreferences() {
    try {
      const currentUser = authService.getCurrentUser();
      
      if (!currentUser) {
        throw new ApiError('User not authenticated', 401);
      }

      const cacheKey = `user_preferences_${currentUser.id}`;
      
      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockData = await this.getMockUserPreferences();
        this.setCache(cacheKey, mockData);
        return mockData;
      }

      // Real API call
      const response = await apiClient.get(`/users/${currentUser.id}/preferences`);
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error fetching user preferences:', error);
      throw new ApiError(
        error.message || 'Failed to fetch user preferences',
        error.status || 500
      );
    }
  }

  // Get mock user preferences for development
  async getMockUserPreferences() {
    await new Promise(resolve => setTimeout(resolve, 200));

    return {
      language: 'id',
      timezone: 'Asia/Jakarta',
      notifications: {
        email: true,
        push: true,
        sms: false,
        training: true,
        updates: true,
        reminders: true,
      },
      theme: 'light',
      autoScroll: true,
      videoQuality: 'auto',
      autoPlay: false,
      subtitles: false,
      privacy: {
        profileVisibility: 'internal',
        activityTracking: true,
        dataSharing: false,
      },
    };
  }

  // Update user preferences
  async updateUserPreferences(preferences) {
    try {
      const currentUser = authService.getCurrentUser();
      
      if (!currentUser) {
        throw new ApiError('User not authenticated', 401);
      }

      // For development - simulate update
      if (__DEV__) {
        const mockData = await this.mockUpdateUserPreferences(preferences);
        
        // Clear cache to force refresh
        this.clearUserCache(currentUser.id);
        
        return mockData;
      }

      // Real API call
      const response = await apiClient.put(`/users/${currentUser.id}/preferences`, preferences);
      const { data } = response;

      // Clear cache to force refresh
      this.clearUserCache(currentUser.id);

      return data;

    } catch (error) {
      console.error('Error updating user preferences:', error);
      throw new ApiError(
        error.message || 'Failed to update user preferences',
        error.status || 500
      );
    }
  }

  // Mock update user preferences for development
  async mockUpdateUserPreferences(preferences) {
    await new Promise(resolve => setTimeout(resolve, 400));

    console.log('Mock updating user preferences:', preferences);

    return {
      success: true,
      message: 'Preferences updated successfully',
      updatedPreferences: preferences,
    };
  }

  // Get user activity history
  async getUserActivity(options = {}) {
    try {
      const currentUser = authService.getCurrentUser();
      
      if (!currentUser) {
        throw new ApiError('User not authenticated', 401);
      }

      const { limit = 20, offset = 0, type = null } = options;
      const cacheKey = `user_activity_${currentUser.id}_${limit}_${offset}_${type}`;
      
      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockData = await this.getMockUserActivity(options);
        this.setCache(cacheKey, mockData);
        return mockData;
      }

      // Real API call
      const params = { limit, offset };
      if (type) params.type = type;

      const response = await apiClient.get(`/users/${currentUser.id}/activity`, params);
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error fetching user activity:', error);
      throw new ApiError(
        error.message || 'Failed to fetch user activity',
        error.status || 500
      );
    }
  }

  // Get mock user activity for development
  async getMockUserActivity(options = {}) {
    const { limit = 20, offset = 0 } = options;
    
    await new Promise(resolve => setTimeout(resolve, 300));

    const mockActivities = [
      {
        id: 1,
        type: 'login',
        title: 'Check-in berhasil',
        description: 'Anda telah melakukan check-in pada pukul 07:45',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        icon: 'check-circle',
        color: 'success',
      },
      {
        id: 2,
        type: 'approval',
        title: 'Pengajuan cuti disetujui',
        description: 'Pengajuan cuti tanggal 25-27 Januari telah disetujui',
        timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(), // 5 hours ago
        icon: 'calendar-check',
        color: 'primary',
      },
      {
        id: 3,
        type: 'training',
        title: 'Training Safety wajib',
        description: 'Anda memiliki training keselamatan kerja yang harus diselesaikan',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        icon: 'shield-check',
        color: 'warning',
      },
      {
        id: 4,
        type: 'report',
        title: 'Laporan produksi dikirim',
        description: 'Laporan produksi harian telah berhasil dikirim',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        icon: 'file-text',
        color: 'info',
      },
      {
        id: 5,
        type: 'system',
        title: 'Update sistem maintenance',
        description: 'Sistem CNM akan mengalami maintenance pada 20 Januari',
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
        icon: 'settings',
        color: 'secondary',
      },
    ];

    // Apply pagination
    const startIndex = offset;
    const endIndex = startIndex + limit;
    const paginatedActivities = mockActivities.slice(startIndex, endIndex);

    return {
      activities: paginatedActivities,
      total: mockActivities.length,
      limit,
      offset,
      hasMore: endIndex < mockActivities.length,
    };
  }

  // Change password
  async changePassword(passwordData) {
    try {
      const currentUser = authService.getCurrentUser();
      
      if (!currentUser) {
        throw new ApiError('User not authenticated', 401);
      }

      const { currentPassword, newPassword, confirmPassword } = passwordData;

      // Validate input
      if (!currentPassword || !newPassword || !confirmPassword) {
        throw new ApiError('All password fields are required', 400);
      }

      if (newPassword !== confirmPassword) {
        throw new ApiError('New passwords do not match', 400);
      }

      if (newPassword.length < 6) {
        throw new ApiError('New password must be at least 6 characters', 400);
      }

      // For development - simulate password change
      if (__DEV__) {
        const mockData = await this.mockChangePassword(passwordData);
        return mockData;
      }

      // Real API call
      const response = await apiClient.put(`/users/${currentUser.id}/password`, {
        currentPassword,
        newPassword,
      });

      return response.data;

    } catch (error) {
      console.error('Error changing password:', error);
      throw new ApiError(
        error.message || 'Failed to change password',
        error.status || 500
      );
    }
  }

  // Mock change password for development
  async mockChangePassword(passwordData) {
    await new Promise(resolve => setTimeout(resolve, 800));

    console.log('Mock changing password');

    return {
      success: true,
      message: 'Password changed successfully',
    };
  }

  // Cache management
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  isCacheValid(key) {
    if (!this.cache.has(key)) {
      return false;
    }
    
    const cached = this.cache.get(key);
    return (Date.now() - cached.timestamp) < this.cacheExpiry;
  }

  clearUserCache(userId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(`_${userId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  clearCache() {
    this.cache.clear();
  }
}

// Create singleton instance
const userService = new UserService();

export { userService };
export default userService;
