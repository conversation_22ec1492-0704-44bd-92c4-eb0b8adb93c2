// 8px Grid System for consistent spacing
export const Spacing = {
  // Base unit (8px)
  unit: 8,
  
  // Spacing scale
  xs: 4,   // 0.5 * unit
  sm: 8,   // 1 * unit
  md: 16,  // 2 * unit
  lg: 24,  // 3 * unit
  xl: 32,  // 4 * unit
  xxl: 40, // 5 * unit
  xxxl: 48, // 6 * unit
  
  // Component specific spacing
  padding: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  
  margin: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  
  // Layout spacing
  container: 16,
  section: 24,
  component: 16,
  
  // Border radius
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    xxl: 20,
    round: 50,
  },
  
  // Icon sizes
  iconSize: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 32,
    xl: 40,
  },
  
  // Button heights
  buttonHeight: {
    sm: 32,
    md: 40,
    lg: 48,
    xl: 56,
  },
  
  // Card spacing
  card: {
    padding: 16,
    margin: 8,
    borderRadius: 12,
  },
};
