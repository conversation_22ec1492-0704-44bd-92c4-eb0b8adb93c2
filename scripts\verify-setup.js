#!/usr/bin/env node

/**
 * Setup Verification Script for PSG-BMI Portal
 * This script verifies that all required files and dependencies are in place
 */

const fs = require('fs');
const path = require('path');

const requiredFiles = [
  'package.json',
  'app.json',
  'babel.config.js',
  'App.js',
  'src/components/index.js',
  'src/constants/index.js',
  'src/utils/index.js',
  'src/screens/HomeScreen.js',
];

const requiredComponents = [
  'src/components/Button.js',
  'src/components/Card.js',
  'src/components/MenuCard.js',
  'src/components/StatCard.js',
  'src/components/Header.js',
  'src/components/MenuGrid.js',
  'src/components/Carousel.js',
  'src/components/DashboardOverview.js',
  'src/components/VideoGallery.js',
  'src/components/RecentActivities.js',
  'src/components/BottomNavigation.js',
  'src/components/ErrorBoundary.js',
];

const requiredConstants = [
  'src/constants/Colors.js',
  'src/constants/Typography.js',
  'src/constants/Spacing.js',
  'src/constants/Animations.js',
  'src/constants/MenuData.js',
];

function checkFile(filePath) {
  const fullPath = path.join(process.cwd(), filePath);
  const exists = fs.existsSync(fullPath);
  console.log(`${exists ? '✅' : '❌'} ${filePath}`);
  return exists;
}

function checkPackageJson() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = [
      'expo',
      'react',
      'react-native',
      'react-native-safe-area-context',
      '@expo/vector-icons',
    ];

    console.log('\n📦 Checking package.json dependencies:');
    let allDepsPresent = true;

    requiredDeps.forEach(dep => {
      const exists = packageJson.dependencies && packageJson.dependencies[dep];
      console.log(`${exists ? '✅' : '❌'} ${dep}`);
      if (!exists) allDepsPresent = false;
    });

    return allDepsPresent;
  } catch (error) {
    console.log('❌ Error reading package.json:', error.message);
    return false;
  }
}

function main() {
  console.log('🔍 PSG-BMI Portal Setup Verification\n');

  console.log('📁 Checking required files:');
  const filesOk = requiredFiles.every(checkFile);

  console.log('\n🧩 Checking components:');
  const componentsOk = requiredComponents.every(checkFile);

  console.log('\n⚙️ Checking constants:');
  const constantsOk = requiredConstants.every(checkFile);

  const depsOk = checkPackageJson();

  console.log('\n📊 Summary:');
  console.log(`Files: ${filesOk ? '✅' : '❌'}`);
  console.log(`Components: ${componentsOk ? '✅' : '❌'}`);
  console.log(`Constants: ${constantsOk ? '✅' : '❌'}`);
  console.log(`Dependencies: ${depsOk ? '✅' : '❌'}`);

  const allOk = filesOk && componentsOk && constantsOk && depsOk;

  if (allOk) {
    console.log('\n🎉 Setup verification completed successfully!');
    console.log('You can now run: npm start');
  } else {
    console.log('\n⚠️ Some issues found. Please check the missing files/dependencies.');
    process.exit(1);
  }
}

main();
